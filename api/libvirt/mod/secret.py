#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Libvirt Secret 管理模块
用于管理虚拟机访问 Ceph RBD 时的认证密钥
"""

import tempfile
import os
import re
import logging
from typing import Optional, Tuple, List, Dict


class SecretManager:
    """Libvirt Secret 管理类"""

    def __init__(self):
        """初始化 Secret 管理器"""
        self.logger = logging.getLogger(__name__)

    def create_ceph_secret(self, username: str, keyring: str,
                          secret_name: Optional[str] = None) -> Tuple[bool, str]:
        """
        创建 Ceph 类型的 libvirt secret

        Args:
            client: libvirt 客户端实例
            username: Ceph 用户名
            keyring: Ceph 密钥（base64 编码）
            secret_name: Secret 名称，默认为 "client.{username} secret"

        Returns:
            (success, uuid_or_error): 成功标志和 UUID 或错误信息
        """
        try:
            if secret_name is None:
                secret_name = f"client.{username} secret"

            # 创建 Secret XML 定义
            secret_xml = f"""<secret ephemeral='no' private='yes'>
  <usage type='ceph'>
    <name>{secret_name}</name>
  </usage>
</secret>"""

            self.logger.info(f"创建 Ceph Secret: {secret_name}")

            # 定义 secret
            secret = self.client.conn.secretDefineXML(secret_xml)
            if secret is None:
                return False, "定义 secret 失败"

            # 获取 UUID
            uuid = secret.UUIDString()

            # 设置 secret 值
            if secret.setValue(keyring.encode('utf-8')) != 0:
                # 如果设置值失败，删除已创建的 secret
                secret.undefine()
                return False, "设置 secret 值失败"

            self.logger.info(f"成功创建 Ceph Secret: {uuid} (用户: {username})")
            return True, uuid

        except Exception as e:
            error_msg = f"创建 Ceph Secret 异常: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def delete_secret(self, uuid: str) -> Tuple[bool, str]:
        """
        删除 libvirt secret

        Args:
            client: libvirt 客户端实例
            uuid: Secret UUID

        Returns:
            (success, message): 成功标志和消息
        """
        try:
            # 查找 secret
            secret = self.client.conn.secretLookupByUUIDString(uuid)
            if secret is None:
                return False, f"未找到 UUID 为 {uuid} 的 secret"

            # 删除 secret
            if secret.undefine() != 0:
                return False, f"删除 secret {uuid} 失败"

            message = f"成功删除 secret: {uuid}"
            self.logger.info(message)
            return True, message

        except Exception as e:
            error_msg = f"删除 secret 异常: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def list_secrets(self) -> Tuple[bool, List[Dict]]:
        """
        列出所有 libvirt secrets

        Args:
            client: libvirt 客户端实例

        Returns:
            (success, secrets): 成功标志和 secret 列表
        """
        try:
            secrets = []

            # 获取所有 secrets
            secret_uuids = self.client.conn.listSecrets()

            for uuid in secret_uuids:
                try:
                    secret = self.client.conn.secretLookupByUUIDString(uuid)
                    if secret is not None:
                        # 获取 XML 定义
                        xml = secret.XMLDesc()

                        # 解析基本信息
                        secret_info = {
                            'uuid': uuid,
                            'xml': xml
                        }

                        # 提取使用类型
                        usage_match = re.search(r"<usage type='([^']+)'>", xml)
                        if usage_match:
                            secret_info['usage_type'] = usage_match.group(1)

                        # 提取名称
                        name_match = re.search(r"<name>([^<]+)</name>", xml)
                        if name_match:
                            secret_info['name'] = name_match.group(1)

                        secrets.append(secret_info)

                except Exception as e:
                    self.logger.warning(f"获取 secret {uuid} 信息失败: {str(e)}")
                    continue

            self.logger.info(f"列出 {len(secrets)} 个 secrets")
            return True, secrets

        except Exception as e:
            error_msg = f"列出 secrets 异常: {str(e)}"
            self.logger.error(error_msg)
            return False, []

    def get_secret_info(self, uuid: str) -> Tuple[bool, Dict]:
        """
        获取 secret 详细信息

        Args:
            client: libvirt 客户端实例
            uuid: Secret UUID

        Returns:
            (success, info): 成功标志和 secret 信息
        """
        try:
            secret = self.client.conn.secretLookupByUUIDString(uuid)
            if secret is None:
                return False, {"error": f"未找到 UUID 为 {uuid} 的 secret"}

            # 获取 XML 定义
            xml = secret.XMLDesc()

            info = {
                'uuid': uuid,
                'xml': xml
            }

            # 解析 XML 获取详细信息
            usage_match = re.search(r"<usage type='([^']+)'>", xml)
            if usage_match:
                info['usage_type'] = usage_match.group(1)

            name_match = re.search(r"<name>([^<]+)</name>", xml)
            if name_match:
                info['name'] = name_match.group(1)

            # 检查是否为私有和临时
            if "private='yes'" in xml:
                info['private'] = True
            if "ephemeral='no'" in xml:
                info['persistent'] = True

            self.logger.info(f"获取 secret 信息成功: {uuid}")
            return True, info

        except Exception as e:
            error_msg = f"获取 secret 信息异常: {str(e)}"
            self.logger.error(error_msg)
            return False, {"error": error_msg}

    def secret_exists(self, uuid: str) -> bool:
        """
        检查 secret 是否存在

        Args:
            client: libvirt 客户端实例
            uuid: Secret UUID

        Returns:
            bool: Secret 是否存在
        """
        try:
            secret = self.client.conn.secretLookupByUUIDString(uuid)
            return secret is not None
        except:
            return False

    def find_secret_by_name(self, name: str) -> Tuple[bool, Optional[str]]:
        """
        根据名称查找 secret UUID

        Args:
            client: libvirt 客户端实例
            name: Secret 名称

        Returns:
            (success, uuid): 成功标志和 UUID（如果找到）
        """
        try:
            success, secrets = self.list_secrets(self.client)
            if not success:
                return False, None

            for secret in secrets:
                if secret.get('name') == name:
                    return True, secret['uuid']

            return True, None  # 没找到但操作成功

        except Exception as e:
            error_msg = f"查找 secret 异常: {str(e)}"
            self.logger.error(error_msg)
            return False, None

    def get_or_create_ceph_secret(self, username: str, keyring: str,
                                 secret_name: Optional[str] = None) -> Tuple[bool, str]:
        """
        获取或创建 Ceph secret（如果不存在则创建）

        Args:
            client: libvirt 客户端实例
            username: Ceph 用户名
            keyring: Ceph 密钥
            secret_name: Secret 名称

        Returns:
            (success, uuid_or_error): 成功标志和 UUID 或错误信息
        """
        try:
            if secret_name is None:
                secret_name = f"client.{username} secret"

            # 先尝试查找现有的 secret
            success, uuid = self.find_secret_by_name(self.client, secret_name)

            if success and uuid is not None:
                self.logger.info(f"使用现有的 secret: {uuid}")
                return True, uuid

            # 如果不存在，则创建新的
            return self.create_ceph_secret(self.client, username, keyring, secret_name)

        except Exception as e:
            error_msg = f"获取或创建 secret 异常: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def generate_disk_xml(self, pool_name: str, volume_name: str, username: str,
                         secret_uuid: str, monitors: List[str], target_dev: str = "vdb",
                         bus: str = "virtio") -> str:
        """
        生成 Ceph RBD 磁盘的 XML 配置

        Args:
            pool_name: Ceph 池名称
            volume_name: RBD 卷名称
            username: Ceph 用户名
            secret_uuid: Secret UUID
            monitors: Monitor 节点列表 ["ip1:port1", "ip2:port2"]
            target_dev: 目标设备名称
            bus: 总线类型

        Returns:
            str: 磁盘 XML 配置
        """
        # 构建 host 节点
        host_nodes = []
        for monitor in monitors:
            if ':' in monitor:
                ip, port = monitor.split(':', 1)
            else:
                ip, port = monitor, '6789'
            host_nodes.append(f"    <host name='{ip}' port='{port}'/>")

        hosts_xml = '\n'.join(host_nodes)

        disk_xml = f"""  <disk type='network' device='disk'>
    <driver name='qemu' type='raw'/>
    <auth username='{username}'>
      <secret type='ceph' uuid='{secret_uuid}'/>
    </auth>
    <source protocol='rbd' name='{pool_name}/{volume_name}'>
{hosts_xml}
    </source>
    <target dev='{target_dev}' bus='{bus}'/>
  </disk>"""

        return disk_xml