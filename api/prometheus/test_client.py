import unittest
from unittest.mock import patch


IP = "**************"
# Patch the settings module for testing
class TestClient(unittest.TestCase):
    @patch('settings.RROMETHEUS_ALERT_URI', "http://%s:9090/api/v1/alerts" % IP)
    @patch('settings.RROMETHEUS_QUERY_URI', "http://%s:9090/api/v1/query" % IP)
    def test_client_urls(self):
        from client import Client  # Importing here to apply patches

        client = Client()
        res = client.DockerClient.get_ceph_osd_mem_usage_rate(client)
        #print(res)
        # self.assertEqual(client.get_alert_url(), "http://test-alert-uri")
        # self.assertEqual(client.get_query_url(), "http://test-query-uri")


    @patch('settings.ALERTMANAGER_URI', "http://%s:9093" % IP)
    def test_client_alert_list(self):
        from client_alert import AlertClient  # Importing here to apply patches

        client = AlertClient()
        #res = client.get_alert_manager_alert()
        res = client.get_alert_ceph_alert()
        print(res)
        # self.assertEqual(client.get_alert_url(), "http://test-alert-uri")
        # self.assertEqual(client.get_query_url(), "http://test-query-uri")


if __name__ == '__main__':
    unittest.main()