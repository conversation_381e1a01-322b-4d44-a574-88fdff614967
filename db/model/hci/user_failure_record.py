from sqlalchemy import Column, <PERSON><PERSON>ey, Integer, String, <PERSON>olean, Enum, JSON
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship
import uuid
import enum
from db.model.hci.base import BaseModel

class DHCPRangeType(enum.Enum):
    RANGE = 'range'
    LIST = 'list'
    EXCLUDE = 'exclude'

class Subnet(BaseModel):
    __tablename__ = 'subnets'
    
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    network_id = Column(Integer, ForeignKey('networks.id'), nullable=False, comment="网络id")
    cidr = Column(String(32), nullable=False, comment="cidr地址范围")
    gateway_ip = Column(String(32), nullable=False, comment="默认网关")
    enable_dhcp = Column(Boolean, default=True, nullable=False, comment="是否启用dhcp")
    dns_nameservers = Column(JSON, comment="dns服务器列表")  # 使用JSON类型存储DNS服务器列表
    host_routes = Column(JSON, comment="路由表")  # 使用JSON类型存储路由表
    ipv6_mode = Column(String(32), comment="ipv6配置模式：slaac, dhcp6, none")
    status = Column(String(32), comment="子网状态")
    vlan_id = Column(PG_UUID(as_uuid=True), ForeignKey('vlan.id'))
    remark = Column(String(512))

    # 关联DHCP范围配置
    dhcp_ranges = relationship("DHCPRange", back_populates="subnet", cascade="all, delete-orphan")

class DHCPRange(BaseModel):
    __tablename__ = 'dhcp_ranges'
    
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, unique=True, nullable=False)
    subnet_id = Column(PG_UUID(as_uuid=True), ForeignKey('subnets.id'), nullable=False)
    type = Column(Enum(DHCPRangeType), nullable=False, comment="DHCP范围类型：range, list, exclude")
    
    # 使用JSON类型存储IP范围和地址列表，便于灵活配置
    # ranges格式: [{"start": "*************", "end": "*************"}, ...]
    ranges = Column(JSON, comment="IP地址范围列表")
    
    # addresses格式: ["************", "************", ...]
    addresses = Column(JSON, comment="离散IP地址列表")
    
    subnet = relationship("Subnet", back_populates="dhcp_ranges")

    def to_dict(self):
        """转换为字典格式，便于序列化"""
        return {
            'id': str(self.id),
            'type': self.type.value,
            'ranges': self.ranges or [],
            'addresses': self.addresses or []
        }

# 使用示例：
"""
# 创建子网
subnet = Subnet(
    network_id=1,
    cidr='***********/24',
    gateway_ip='***********',
    enable_dhcp=True,
    dns_nameservers=['*******', '*******']
)
029_add_user_failure_record
# 添加DHCP范围配置
range_config = DHCPRange(
    type=DHCPRangeType.RANGE,
    ranges=[
        {'start': '*************', 'end': '*************'},
        {'start': '*************', 'end': '*************'}
    ]
)

list_config = DHCPRange(
    type=DHCPRangeType.LIST,
    addresses=['************', '************', '************']
)

exclude_config = DHCPRange(
    type=DHCPRangeType.EXCLUDE,
    ranges=[{'start': '***********80', 'end': '***********85'}],
    addresses=['***********50', '*************']
)

subnet.dhcp_ranges.extend([range_config, list_config, exclude_config])
"""