from sqlalchemy import create_engine, Column, Integer, String, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import logging

# 设置日志记录，便于调试
logging.basicConfig()
logging.getLogger('sqlalchemy.engine').setLevel(logging.DEBUG)

# 创建模型基类
Base = declarative_base()

# 定义 migrate_version 表的 ORM 模型
class MigrateVersion(Base):
    __tablename__ = 'migrate_version'
    
    repository_id = Column(String(250), primary_key=True)
    repository_path = Column(Text)
    version = Column(Integer)

# 连接到 dqlite 数据库
engine = create_engine('dqlite+pydqlite://192.168.213.30:9001/hci_db', echo=True)

# 创建数据库会话
Session = sessionmaker(bind=engine)
session = Session()

# 创建表（如果不存在）
Base.metadata.create_all(engine)

# 插入一个测试数据
def insert_test_data():
    new_version = MigrateVersion(
        repository_id="theweb",
        repository_path="/path/to/repository",
        version=2
    )
    session.add(new_version)
    session.commit()

# 查询 migrate_version 表
def query_migrate_version():
    result = session.query(MigrateVersion).all()
    if result:
        for row in result:
            print("----------------")
            print(f"ID: {row.repository_id}, Path: {row.repository_path}, Version: {row.version}")
    else:
        print("No rows returned from the query")
        
def query_with_raw_sql():
    with engine.connect() as connection:
        result = connection.execute("SELECT * FROM migrate_version")
        rows = result.fetchall()
        for row in rows:
            print("----------------")
            print(row)
            

# 主程序
if __name__ == "__main__":
    # 插入测试数据
    #insert_test_data()
    
    # 查询并打印结果
    query_migrate_version()
    
    #query_with_raw_sql()